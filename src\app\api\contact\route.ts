import { NextResponse } from "next/server";

// Add runtime configuration
export const runtime = "nodejs";
export const dynamic = "force-dynamic";

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, email, subject, message } = body;

    // Log the contact form submission (remove in production)
    console.log("Contact form submission:", { name, email, subject, message });

    // --- SIMULASI PENGIRIMAN EMAIL ---
    // <PERSON> l<PERSON>ngan produksi, <PERSON><PERSON> akan mengintegrasikan layanan pengiriman email di sini,
    // se<PERSON><PERSON>demailer, SendGrid, Mailgun, atau layanan email lainnya.
    // Contoh:
    // await sendEmail({
    //   to: '<EMAIL>',
    //   from: email,
    //   subject: `Pesan Kontak dari ${name}: ${subject}`,
    //   text: message,
    // });

    return NextResponse.json(
      { message: "Pesan berhasil diterima!" },
      { status: 200 }
    );
  } catch (error: unknown) {
    console.error("Error handling contact form submission:", error);
    return NextResponse.json(
      { error: "Gagal memproses pesan Anda." },
      { status: 500 }
    );
  }
}
