// This file is automatically generated. Do not edit it directly.
import { createBrowserClient } from "@supabase/ssr";

const SUPABASE_URL =
  process.env.NEXT_PUBLIC_SUPABASE_URL ||
  "https://xlvnaempudqlrdonfzun.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhsdm5hZW1wdWRxbHJkb25menVuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5OTA3NTQsImV4cCI6MjA2NzU2Njc1NH0.JICLB7UxI6qq-72nyLV4kizTs38NRDYtHSwTASa52K8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createBrowserClient(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storage: typeof window !== "undefined" ? window.localStorage : undefined,
      // Disable debug mode to reduce warnings
      debug: false,
      // Add retry configuration for better connection reliability
      flowType: "pkce",
    },
    // Enhanced configuration for better connection reliability
    global: {
      headers: {
        "X-Client-Info": "supabase-js-web",
      },
    },
    // Add connection pooling and retry logic
    db: {
      schema: "public",
    },
    // Realtime configuration for better connection handling
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  }
);
