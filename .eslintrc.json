{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "react-hooks/rules-of-hooks": "off", "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-unsafe-function-type": "off"}}