{"dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@rainbow-me/rainbowkit": "^2.2.8", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-wallets": "^0.19.37", "@solana/web3.js": "^1.98.2", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.25", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "next": "15.3.4", "next-themes": "^0.4.6", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "viem": "^2.31.7", "wagmi": "^2.15.6", "zod": "^3.25.28"}, "devDependencies": {"@dyad-sh/nextjs-webpack-component-tagger": "^0.8.0", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.31.0", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5"}, "scripts": {"dev": "next dev --turbopack", "build": "next build && node scripts/suppress-production-warnings.js", "build:clean": "next build", "start": "next start", "lint": "next lint", "suppress-warnings": "node scripts/suppress-production-warnings.js", "fix-analytics": "tsx src/scripts/run-analytics-fix.ts"}}